import { createBullBoard } from '@bull-board/api';
import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { ExpressAdapter } from '@bull-board/express';
import { Express } from 'express';
import { QueueService } from './queue-service';

export class DashboardService {
  private static serverAdapter: ExpressAdapter;
  private static isInitialized = false;

  // Initialize Bull Board dashboard
  static initialize(app: Express): void {
    if (this.isInitialized) {
      return;
    }

    try {
      // Create Express adapter for Bull Board
      this.serverAdapter = new ExpressAdapter();
      this.serverAdapter.setBasePath('/admin/queues');

      // Create Bull Board with queue adapters
      createBullBoard({
        queues: [
          new BullMQAdapter(QueueService.getQueue()),
        ],
        serverAdapter: this.serverAdapter,
      });

      // Mount the Bull Board UI
      app.use('/admin/queues', this.serverAdapter.getRouter());

      console.log('🎯 Bull Board Dashboard initialized at /admin/queues');
      this.isInitialized = true;

    } catch (error) {
      console.error('Failed to initialize Bull Board Dashboard:', error);
      throw error;
    }
  }

  // Add queue statistics endpoint
  static addStatsEndpoint(app: Express): void {
    app.get('/api/queue/stats', async (req, res) => {
      try {
        const stats = await QueueService.getQueueStats();
        res.json({
          success: true,
          data: stats,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error('Failed to get queue stats:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to get queue statistics',
        });
      }
    });

    app.get('/api/queue/position/:jobId', async (req, res) => {
      try {
        const { jobId } = req.params;
        const position = await QueueService.getJobPosition(jobId);
        res.json({
          success: true,
          data: { position },
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error('Failed to get job position:', error);
        res.status(500).json({
          success: false,
          error: 'Failed to get job position',
        });
      }
    });

    console.log('📊 Queue statistics endpoints added');
  }
}
