import { TestRunnerService } from './test-runner';

// Simple in-memory queue without Redis dependency
interface TestJobData {
  apiKey: string;
  testData: {
    testCaseId: string;
    tcId: string;
    steps: any[];
    testCase: any;
    authToken?: string;
    projectId?: string;
    testRunId?: string;
  };
  clientId: string;
  timestamp: number;
  jobId: string;
}

export class SimpleQueueService {
  private static queue: TestJobData[] = [];
  private static isProcessing = false;
  private static jobIdCounter = 1;
  private static completedJobs: TestJobData[] = [];
  private static failedJobs: TestJobData[] = [];

  // Add a test job to the queue
  static async addTestJob(
    apiKey: string,
    testData: any,
    clientId: string
  ): Promise<{ jobId: string; position: number }> {
    const jobId = `job-${this.jobIdCounter++}`;
    
    const job: TestJobData = {
      apiKey,
      testData,
      clientId,
      timestamp: Date.now(),
      jobId,
    };

    this.queue.push(job);
    const position = this.queue.length;

    console.log(`📝 Test job ${jobId} added to queue for API key: ${apiKey}, position: ${position}`);

    // Notify client about queue status
    this.notifyJobStatus(clientId, 'waiting', job);

    // Start processing if not already processing
    if (!this.isProcessing) {
      this.processQueue();
    }

    return { jobId, position };
  }

  // Process the queue
  private static async processQueue(): Promise<void> {
    if (this.isProcessing || this.queue.length === 0) {
      return;
    }

    this.isProcessing = true;

    while (this.queue.length > 0) {
      const job = this.queue.shift()!;
      
      try {
        console.log(`🔄 Processing test job ${job.jobId} for API key: ${job.apiKey}`);
        
        // Notify that job is now active
        this.notifyJobStatus(job.clientId, 'active', job);

        // Check if client is still connected
        if (!TestRunnerService.isClientConnected(job.apiKey)) {
          console.log(`❌ Client ${job.apiKey} is no longer connected, skipping job ${job.jobId}`);
          this.failedJobs.push(job);
          continue;
        }

        // Execute the test using the existing TestRunnerService
        await TestRunnerService.executeTest(job.apiKey, job.testData);

        // Mark as completed
        this.completedJobs.push(job);
        this.notifyJobStatus(job.clientId, 'completed', job);

        console.log(`✅ Test job ${job.jobId} completed successfully for API key: ${job.apiKey}`);

      } catch (error) {
        console.error(`❌ Test job ${job.jobId} failed for API key: ${job.apiKey}:`, error);
        this.failedJobs.push(job);
        this.notifyJobStatus(job.clientId, 'failed', job, error instanceof Error ? error.message : 'Unknown error');
      }

      // Small delay between jobs to prevent overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.isProcessing = false;
  }

  // Notify client about job status changes
  private static notifyJobStatus(
    clientId: string,
    status: 'waiting' | 'active' | 'completed' | 'failed',
    jobData: TestJobData,
    error?: string
  ): void {
    try {
      const ws = TestRunnerService.getClientConnection(jobData.apiKey);
      if (ws && ws.readyState === 1) { // WebSocket.OPEN
        ws.send(JSON.stringify({
          type: 'queue_status',
          status,
          jobId: jobData.jobId,
          apiKey: jobData.apiKey,
          timestamp: Date.now(),
          error: error || undefined,
        }));
      }
    } catch (err) {
      console.error('Failed to notify client about job status:', err);
    }
  }

  // Get queue statistics
  static async getQueueStats(): Promise<any> {
    return {
      waiting: this.queue.length,
      active: this.isProcessing ? 1 : 0,
      completed: this.completedJobs.length,
      failed: this.failedJobs.length,
      total: this.queue.length + (this.isProcessing ? 1 : 0),
    };
  }

  // Get job position in queue
  static async getJobPosition(jobId: string): Promise<number> {
    const position = this.queue.findIndex(job => job.jobId === jobId) + 1;
    return position || 0;
  }

  // Clean up old completed and failed jobs
  static async cleanQueue(): Promise<void> {
    const maxCompleted = 10;
    const maxFailed = 20;

    if (this.completedJobs.length > maxCompleted) {
      this.completedJobs = this.completedJobs.slice(-maxCompleted);
    }

    if (this.failedJobs.length > maxFailed) {
      this.failedJobs = this.failedJobs.slice(-maxFailed);
    }

    console.log('🧹 Queue cleaned successfully');
  }

  // Initialize the service (no-op for simple queue)
  static async initialize(): Promise<void> {
    console.log('🚀 Simple Queue Service initialized successfully');
    
    // Clean up old jobs periodically
    setInterval(() => {
      this.cleanQueue();
    }, 60000); // Clean every minute
  }

  // Graceful shutdown
  static async shutdown(): Promise<void> {
    console.log('🔄 Shutting down Simple Queue Service...');
    
    // Wait for current job to complete if processing
    while (this.isProcessing) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    // Clear the queue
    this.queue = [];
    this.completedJobs = [];
    this.failedJobs = [];
    
    console.log('✅ Simple Queue Service shut down successfully');
  }

  // Get current queue status for debugging
  static getQueueStatus(): any {
    return {
      queueLength: this.queue.length,
      isProcessing: this.isProcessing,
      completedCount: this.completedJobs.length,
      failedCount: this.failedJobs.length,
      currentJobs: this.queue.map(job => ({
        jobId: job.jobId,
        apiKey: job.apiKey,
        timestamp: job.timestamp,
      })),
    };
  }
}
