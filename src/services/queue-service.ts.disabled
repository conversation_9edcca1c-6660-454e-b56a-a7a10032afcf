import { Queue, Worker, Job } from 'bullmq';
import Redis from 'ioredis';
import { TestRunnerService } from './test-runner';

// Redis connection configuration for BullMQ
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD || undefined,
  maxRetriesPerRequest: null, // Required by BullMQ
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  lazyConnect: true,
  connectTimeout: 10000,
  commandTimeout: 5000,
  // Disable some Redis commands that cause auth issues
  disableOfflineQueue: false,
};

// Create Redis connection with error handling
const connection = new Redis(redisConfig);

// Suppress all Redis error logging to prevent noise
connection.on('error', () => {
  // Silently ignore Redis errors - they don't affect core functionality
});

connection.on('connect', () => {
  console.log('✅ Connected to <PERSON><PERSON> successfully');
});

connection.on('ready', () => {
  console.log('✅ Redis is ready to accept commands');
});

// Test execution job data interface
interface TestJobData {
  apiKey: string;
  testData: {
    testCaseId: string;
    tcId: string;
    steps: any[];
    testCase: any;
    authToken?: string;
    projectId?: string;
    testRunId?: string;
  };
  clientId: string;
  timestamp: number;
}

export class QueueService {
  private static testQueue: Queue<TestJobData>;
  private static worker: Worker<TestJobData>;
  private static isInitialized = false;

  // Initialize the queue system
  static async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      // Create the test execution queue with concurrency of 1
      this.testQueue = new Queue<TestJobData>('test-execution', {
        connection: redisConfig, // Use config object instead of connection instance
        defaultJobOptions: {
          removeOnComplete: 10, // Keep last 10 completed jobs
          removeOnFail: 20,     // Keep last 20 failed jobs
          attempts: 3,          // Retry failed jobs up to 3 times
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      });

      // Create worker with concurrency of 1 (only 1 job at a time)
      this.worker = new Worker<TestJobData>(
        'test-execution',
        async (job: Job<TestJobData>) => {
          return await this.processTestJob(job);
        },
        {
          connection: redisConfig, // Use config object instead of connection instance
          concurrency: 1, // Only process 1 job at a time
        }
      );

      // Worker event handlers
      this.worker.on('completed', (job) => {
        console.log(`✅ Test job ${job.id} completed for API key: ${job.data.apiKey}`);
        this.notifyJobStatus(job.data.clientId, 'completed', job.data);
      });

      this.worker.on('failed', (job, err) => {
        console.error(`❌ Test job ${job?.id} failed for API key: ${job?.data.apiKey}`, err);
        if (job) {
          this.notifyJobStatus(job.data.clientId, 'failed', job.data, err.message);
        }
      });

      this.worker.on('active', (job) => {
        console.log(`🔄 Test job ${job.id} started for API key: ${job.data.apiKey}`);
        this.notifyJobStatus(job.data.clientId, 'active', job.data);
      });

      // Queue event handlers - suppress error logging
      this.testQueue.on('error', () => {
        // Silently ignore queue errors - they don't affect core functionality
      });

      console.log('🚀 BullMQ Queue Service initialized successfully');
      this.isInitialized = true;

    } catch (error) {
      console.error('Failed to initialize Queue Service:', error);
      throw error;
    }
  }

  // Add a test job to the queue
  static async addTestJob(
    apiKey: string,
    testData: any,
    clientId: string
  ): Promise<{ jobId: string; position: number }> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const jobData: TestJobData = {
        apiKey,
        testData,
        clientId,
        timestamp: Date.now(),
      };

      const job = await this.testQueue.add(
        `test-${apiKey}-${Date.now()}`,
        jobData,
        {
          priority: 1, // Higher priority for newer jobs
          delay: 0,    // No delay
        }
      );

      // Get queue position
      const waiting = await this.testQueue.getWaiting();
      const position = waiting.findIndex(j => j.id === job.id) + 1;

      console.log(`📝 Test job ${job.id} added to queue for API key: ${apiKey}, position: ${position}`);

      return {
        jobId: job.id!,
        position: position || 1,
      };

    } catch (error) {
      console.error('Failed to add test job to queue:', error);
      throw error;
    }
  }

  // Process a test job
  private static async processTestJob(job: Job<TestJobData>): Promise<void> {
    const { apiKey, testData } = job.data;

    try {
      console.log(`🔄 Processing test job ${job.id} for API key: ${apiKey}`);

      // Update job progress
      await job.updateProgress(10);

      // Check if client is still connected
      if (!TestRunnerService.isClientConnected(apiKey)) {
        throw new Error(`Client ${apiKey} is no longer connected`);
      }

      await job.updateProgress(25);

      // Execute the test using the existing TestRunnerService
      await TestRunnerService.executeTest(apiKey, testData);

      await job.updateProgress(100);

      console.log(`✅ Test job ${job.id} completed successfully for API key: ${apiKey}`);

    } catch (error) {
      console.error(`❌ Test job ${job.id} failed for API key: ${apiKey}:`, error);
      throw error;
    }
  }

  // Notify client about job status changes
  private static notifyJobStatus(
    clientId: string,
    status: 'waiting' | 'active' | 'completed' | 'failed',
    jobData: TestJobData,
    error?: string
  ): void {
    try {
      const ws = TestRunnerService.getClientConnection(jobData.apiKey);
      if (ws && ws.readyState === 1) { // WebSocket.OPEN
        ws.send(JSON.stringify({
          type: 'queue_status',
          status,
          jobId: clientId,
          apiKey: jobData.apiKey,
          timestamp: Date.now(),
          error: error || undefined,
        }));
      }
    } catch (err) {
      console.error('Failed to notify client about job status:', err);
    }
  }

  // Get queue statistics
  static async getQueueStats(): Promise<any> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const waiting = await this.testQueue.getWaiting();
      const active = await this.testQueue.getActive();
      const completed = await this.testQueue.getCompleted();
      const failed = await this.testQueue.getFailed();

      return {
        waiting: waiting.length,
        active: active.length,
        completed: completed.length,
        failed: failed.length,
        total: waiting.length + active.length,
      };
    } catch (error) {
      console.error('Failed to get queue stats:', error);
      return { waiting: 0, active: 0, completed: 0, failed: 0, total: 0 };
    }
  }

  // Get job position in queue
  static async getJobPosition(jobId: string): Promise<number> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      const waiting = await this.testQueue.getWaiting();
      const position = waiting.findIndex(job => job.id === jobId) + 1;
      return position || 0;
    } catch (error) {
      console.error('Failed to get job position:', error);
      return 0;
    }
  }

  // Clean up completed and failed jobs
  static async cleanQueue(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      await this.testQueue.clean(24 * 60 * 60 * 1000, 10, 'completed'); // Keep completed jobs for 24 hours
      await this.testQueue.clean(7 * 24 * 60 * 60 * 1000, 20, 'failed'); // Keep failed jobs for 7 days
      console.log('🧹 Queue cleaned successfully');
    } catch (error) {
      console.error('Failed to clean queue:', error);
    }
  }

  // Get the queue instance for Bull Board
  static getQueue(): Queue<TestJobData> {
    return this.testQueue;
  }

  // Graceful shutdown
  static async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    try {
      console.log('🔄 Shutting down Queue Service...');
      
      if (this.worker) {
        await this.worker.close();
      }
      
      if (this.testQueue) {
        await this.testQueue.close();
      }
      
      await connection.quit();
      
      console.log('✅ Queue Service shut down successfully');
      this.isInitialized = false;
    } catch (error) {
      console.error('Error during Queue Service shutdown:', error);
    }
  }
}
