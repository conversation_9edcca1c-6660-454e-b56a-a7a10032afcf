import { WebSocket, WebSocketServer } from 'ws';
import { config } from 'dotenv';
import express from 'express';
import cors from 'cors';
import { createServer } from 'http';
import { WSMessage } from './types';
import { TestRunnerService } from './services/test-runner';
import { llmService } from './services/llm-service';
import { QueueService } from './services/queue-service';
import { DashboardService } from './services/dashboard-service';

config();

// Initialize Queue Service and Dashboard
async function initializeServices() {
  try {
    await QueueService.initialize();
    console.log('✅ Queue Service initialized');

    DashboardService.initialize(app);
    DashboardService.addStatsEndpoint(app);
    console.log('✅ Dashboard Service initialized');
  } catch (error) {
    console.warn('⚠️ Failed to initialize Queue/Dashboard services (Redis auth issue):', error instanceof Error ? error.message : error);
    console.log('📝 Server will continue without queue functionality - tests will run directly');
    // Don't exit - continue without queue functionality
  }
}

process.on('SIGTERM', async () => {
  console.log('SIGTERM signal received: closing HTTP server');

  // Graceful shutdown (only if services were initialized)
  try {
    await QueueService.shutdown();
    await DashboardService.cleanup();
  } catch (error) {
    console.log('Services were not initialized, skipping cleanup');
  }

  server.close(() => {
    console.log('HTTP server closed');
    wss.close(() => {
      console.log('WebSocket server closed');
      process.exit(0);
    });
  });
});

process.on('SIGINT', async () => {
  console.log('SIGINT signal received: closing HTTP server');

  // Graceful shutdown (only if services were initialized)
  try {
    await QueueService.shutdown();
    await DashboardService.cleanup();
  } catch (error) {
    console.log('Services were not initialized, skipping cleanup');
  }

  server.close(() => {
    console.log('HTTP server closed');
    wss.close(() => {
      console.log('WebSocket server closed');
      process.exit(0);
    });
  });
});

const app = express();
app.use(cors());
app.use(express.json());

const server = createServer(app);
export const wss = new WebSocketServer({ server });

//Websocket
let apiKey: string | null = null;

const API_VALIDATE_URL = `${process.env.CORE_SERVICE_URL}/profile/validate-api-key`;
const API_PROFILE_BY_KEY_URL = `${process.env.CORE_SERVICE_URL}/profile/profile-by-key`;

wss.on('connection', (ws: WebSocket) => {
  let apiKey: string | null = null;
  
  console.log('Client connected');
  
  // Add connection stabilization timeout
  setTimeout(() => {
    if (ws.readyState === WebSocket.OPEN) {
      console.log('Connection stabilized');
    }
  }, 1000);
  
  ws.on('message', async (message: string) => {
    try {
      const data = JSON.parse(message.toString()) as WSMessage;
      console.log(`Received message type: ${data.type}`); // Add this log

      switch (data.type) {
        case 'auth': {
          if (!data.token) {
            ws.send(JSON.stringify({ type: 'error', message: 'Authentication token is required.' }));
            return;
          }

          try {
            // Log the token being validated (first few characters only for security)
            const tokenPreview = data.token.substring(0, 8) + '...';
            console.log(`Validating API key: ${tokenPreview}`);
            
            const response = await fetch(API_VALIDATE_URL, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': '*/*'
              },
              body: JSON.stringify({ apiKey: data.token }),
            });

            // Log the response for debugging
            console.log(`API validation response status: ${response.status}`);
            
            // Handle non-200 responses more gracefully
            if (!response.ok) {
              console.error('API Key validation failed:', response.status, response.statusText);
              
              // If the core service is unreachable, proceed with a warning
              if (response.status === 404 || response.status === 502 || response.status === 503) {
                console.warn('Core service validation endpoint unreachable, proceeding with authentication');
                apiKey = data.token;
                TestRunnerService.addClient(apiKey, ws);
                ws.send(JSON.stringify({ type: 'auth_success' }));
                return;
              }
              
              // Try to proceed with the token anyway if it looks valid
              if (data.token && data.token.length > 20) {
                console.warn('API validation failed but token looks valid, proceeding with authentication');
                apiKey = data.token;
                TestRunnerService.addClient(apiKey, ws);
                ws.send(JSON.stringify({ type: 'auth_success' }));
                return;
              }
              
              ws.send(JSON.stringify({
                type: 'error',
                message: 'Invalid API key.'
              }));
              return;
            }

            const validationResult = await response.json() as { valid: boolean };

            if (validationResult.valid) {
              // Fetch user profile to check token usage
              try {
                const profileResponse = await fetch(API_PROFILE_BY_KEY_URL, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'Accept': '*/*'
                  },
                  body: JSON.stringify({ apiKey: data.token }),
                });

                if (!profileResponse.ok) {
                  console.error('Failed to fetch profile:', profileResponse.status, profileResponse.statusText);
                  ws.send(JSON.stringify({
                    type: 'error',
                    message: 'Failed to fetch user profile.'
                  }));
                  return;
                }

                const profileData = await profileResponse.json() as { company?: { subscription?: { remainingTokens: number } } };

                if (profileData?.company?.subscription?.remainingTokens === 0) {
                  ws.send(JSON.stringify({
                    type: 'error',
                    message: 'Your remaining token is 0 and cannot continue next step.'
                  }));
                  return;
                }

                apiKey = data.token; // Update the module-level apiKey
                TestRunnerService.addClient(apiKey, ws);
                // Send authentication success message
                ws.send(JSON.stringify({ type: 'auth_success' }));
                console.log(`Authentication successful for ${tokenPreview}, waiting for commands...`);

              } catch (error) {
                console.error('Error fetching user profile:', error);
                ws.send(JSON.stringify({
                  type: 'error',
                  message: 'Error fetching user profile.'
                }));
              }
            } else {
              ws.send(JSON.stringify({
                type: 'error',
                message: 'Invalid API key.'
              }));
            }
          } catch (error) {
            console.error('Error during API key validation:', error);
            ws.send(JSON.stringify({
              type: 'error',
              message: 'Error communicating with the API key validation service.'
            }));
          }
          break;
        }

        case 'ping': {
          ws.send(JSON.stringify({ type: 'pong' }));
          break;
        }

        case 'execute_test': {
          if (!data.token) {
            ws.send(JSON.stringify({ type: 'error', message: 'API key is required for test execution.' }));
            return;
          }

          try {
            // Add test to queue instead of executing directly
            const { jobId, position } = await QueueService.addTestJob(
              data.token,
              {
                testCaseId: data.testCaseId,
                tcId: data.tcId,
                steps: data.steps,
                testCase: data.testCase,
                authToken: data.authToken,
                projectId: data.projectId,
                testRunId: data.testRunId
              },
              `client-${data.token}-${Date.now()}`
            );

            // Get current queue statistics
            const queueStats = await QueueService.getQueueStats();

            // Send queue confirmation to client
            ws.send(JSON.stringify({
              type: 'test_queued',
              jobId,
              position,
              queueStats,
              message: position === 1
                ? 'Your test is starting now...'
                : `Your test is queued at position ${position}. ${queueStats.active > 0 ? 'Another test is currently running.' : ''}`
            }));

            console.log(`✅ Test queued for API key: ${data.token}, Job ID: ${jobId}, Position: ${position}`);

          } catch (error) {
            console.error('Error queuing test:', error);

            // Fallback to direct execution if queue fails
            console.log('⚠️ Queue failed, falling back to direct execution');
            try {
              if (!TestRunnerService.canRunTest(data.token)) {
                ws.send(JSON.stringify({
                  type: 'error',
                  message: 'A test is already running. Please wait for it to complete.'
                }));
                return;
              }

              await TestRunnerService.executeTest(data.token, {
                testCaseId: data.testCaseId,
                tcId: data.tcId,
                steps: data.steps,
                testCase: data.testCase,
                authToken: data.authToken,
                projectId: data.projectId,
                testRunId: data.testRunId
              });

              console.log(`✅ Test executed directly (fallback) for API key: ${data.token}`);
            } catch (fallbackError) {
              console.error('Fallback execution also failed:', fallbackError);
              ws.send(JSON.stringify({
                type: 'test_error',
                message: fallbackError instanceof Error ? fallbackError.message : 'Failed to execute test'
              }));
            }
          }
          break;
        }

        case 'command': {
          if (!data.token) {
            ws.send(JSON.stringify({ type: 'error', message: 'API key is required for commands.' }));
            return;
          }

          try {
            // Validate API Key first
            const validateResponse = await fetch(API_VALIDATE_URL, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': '*/*'
              },
              body: JSON.stringify({ apiKey: data.token }),
            });

            if (!validateResponse.ok) {
              console.error('API Key validation failed:', validateResponse.status, validateResponse.statusText);
              ws.send(JSON.stringify({
                type: 'error',
                message: 'Invalid API key.'
              }));
              return;
            }

            const validationResult = await validateResponse.json() as { valid: boolean };

            if (!validationResult.valid) {
              ws.send(JSON.stringify({
                type: 'error',
                message: 'Invalid API key.'
              }));
              return;
            }

            // Fetch user profile to check token usage
            const profileResponse = await fetch(API_PROFILE_BY_KEY_URL, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': '*/*'
              },
              body: JSON.stringify({ apiKey: data.token }),
            });

            if (!profileResponse.ok) {
              console.error('Failed to fetch profile:', profileResponse.status, profileResponse.statusText);
              ws.send(JSON.stringify({
                type: 'error',
                message: 'Failed to fetch user profile.'
              }));
              return;
            }

            const profileData = await profileResponse.json() as { company?: { subscription?: { remainingTokens: number } } };

            if (profileData?.company?.subscription?.remainingTokens === 0) {
              ws.send(JSON.stringify({
                type: 'error',
                message: 'Your remaining token is 0. Please check your token usage on https://agentq.id.'
              }));
              return;
            }

            if (!data.prompt || !data.pageSource) {
              ws.send(JSON.stringify({
                type: 'error',
                message: 'Prompt and pageSource are required for the command.'
              }));
              return;
            }

            // Process the command through the LLM service with apiKey for token tracking
            const command = await llmService.getCommandFromAI(data.prompt, data.pageSource, data.token);

            if (!command) {
              ws.send(JSON.stringify({
                type: 'error',
                message: 'Failed to generate command from AI'
              }));
              return;
            }

            ws.send(JSON.stringify({ type: 'response', command }));
            break;
          } catch (error) {
            console.error('Error processing command:', error);
            ws.send(JSON.stringify({
              type: 'error',
              message: 'Failed to process command. Please try again.'
            }));
          }
        }
        default:
          ws.send(JSON.stringify({ type: 'error', message: 'Unknown message type.' }));
      }
    } catch (error) {
      console.error('Error processing message:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: 'Failed to process command. Please try again.'
      }));
    }
  });


  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
    if (apiKey) {
      TestRunnerService.removeClient(apiKey);
    }
  });
  
  ws.on('close', () => {
    console.log('Client disconnected');
    
    // If we have the API key, handle disconnection properly
    if (apiKey) {
      console.log(`Client ${apiKey} disconnected`);
      // Now this will work because handleClientDisconnect is a static method
      TestRunnerService.handleClientDisconnect(apiKey);
    }
  });
});

const PORT = process.env.PORT || 3021;

server.on('error', (error: any) => {
  if (error.code === 'EADDRINUSE') {
    console.log('Port 3021 is in use, retrying...');
    setTimeout(() => {
      server.close();
      server.listen(PORT);
    }, 1000);
  }
});

server.listen(PORT, async () => {
  console.log(`Server running on port ${PORT}`);

  // Initialize services after server starts
  await initializeServices();

  console.log(`🎯 Bull Board Dashboard available at: http://localhost:${PORT}/admin/queues`);
  console.log(`📊 Queue Stats API available at: http://localhost:${PORT}/api/queue/stats`);
}).on('error', (error: any) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`Port ${PORT} is already in use`);
    process.exit(1);
  }
});
