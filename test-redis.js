const Redis = require('ioredis');

async function testRedis() {
  console.log('Testing Redis connection...');
  
  // Try without password first
  const redis1 = new Redis({
    host: 'localhost',
    port: 6379,
    lazyConnect: true,
  });

  try {
    await redis1.connect();
    await redis1.ping();
    console.log('✅ Redis connected successfully without password');
    await redis1.quit();
    return;
  } catch (error) {
    console.log('❌ Redis connection failed without password:', error.message);
    await redis1.quit();
  }

  // Try with empty password
  const redis2 = new Redis({
    host: 'localhost',
    port: 6379,
    password: '',
    lazyConnect: true,
  });

  try {
    await redis2.connect();
    await redis2.ping();
    console.log('✅ Redis connected successfully with empty password');
    await redis2.quit();
    return;
  } catch (error) {
    console.log('❌ Redis connection failed with empty password:', error.message);
    await redis2.quit();
  }

  console.log('❌ Could not connect to Redis. Please check your Redis configuration.');
  console.log('You may need to:');
  console.log('1. Set REDIS_PASSWORD in your .env file');
  console.log('2. Or disable Redis authentication');
  console.log('3. Or check if Redis is running: brew services list | grep redis');
}

testRedis().catch(console.error);
