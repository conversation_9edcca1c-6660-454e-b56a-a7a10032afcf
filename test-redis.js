require('dotenv').config();
const Redis = require('ioredis');

async function testRedis() {
  console.log('Testing Redis connection with password from .env...');

  const redis = new Redis({
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    lazyConnect: true,
  });

  try {
    await redis.connect();
    const result = await redis.ping();
    console.log('✅ Redis connected successfully with password!');
    console.log('✅ Ping result:', result);
    await redis.quit();
    return true;
  } catch (error) {
    console.log('❌ Redis connection failed:', error.message);
    await redis.quit();
    return false;
  }
}

testRedis().catch(console.error);
