{"name": "app_frontend_agentq", "version": "1.0.0", "description": "AgentQ Frontend Microservice", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@bull-board/api": "^6.10.1", "@bull-board/express": "^6.10.1", "axios": "^1.6.2", "bullmq": "^5.53.3", "chart.js": "^4.4.8", "ioredis": "^5.6.1", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.0", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20.3.1", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-vue": "^5.1.3", "eslint": "^8.42.0", "prettier": "^3.0.0", "sass": "^1.69.5", "typescript": "^5.5.3", "vite": "^5.4.2", "vue-tsc": "^2.1.4"}}