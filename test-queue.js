const WebSocket = require('ws');

// Test the queue system by sending multiple test requests
async function testQueue() {
  console.log('🧪 Testing BullMQ Queue System...\n');

  // Create multiple WebSocket connections to simulate concurrent users
  const connections = [];
  const numConnections = 3;

  for (let i = 0; i < numConnections; i++) {
    const ws = new WebSocket('ws://localhost:3021');
    const userId = `user${i + 1}`;
    const apiKey = `test-api-key-${i + 1}`;

    ws.on('open', () => {
      console.log(`✅ ${userId} connected`);
      
      // Authenticate first
      ws.send(JSON.stringify({
        type: 'auth',
        token: apiKey
      }));
    });

    ws.on('message', (data) => {
      const message = JSON.parse(data.toString());
      
      switch (message.type) {
        case 'auth_success':
          console.log(`🔐 ${userId} authenticated successfully`);
          
          // Send test execution request
          setTimeout(() => {
            console.log(`📝 ${userId} submitting test...`);
            ws.send(JSON.stringify({
              type: 'execute_test',
              token: apiKey,
              testCaseId: `test-case-${i + 1}`,
              tcId: `tc-${i + 1}`,
              steps: [
                {
                  step: 1,
                  stepName: 'Navigate to page',
                  action: 'goto',
                  target: 'https://example.com'
                }
              ],
              testCase: {
                title: `Test Case ${i + 1}`,
                precondition: 'Browser is open',
                expectation: 'Page loads successfully'
              }
            }));
          }, i * 1000); // Stagger the requests
          break;

        case 'test_queued':
          console.log(`🎯 ${userId} test queued at position ${message.position}`);
          console.log(`   Queue stats: ${message.queueStats.waiting} waiting, ${message.queueStats.active} active`);
          break;

        case 'queue_status':
          console.log(`📊 ${userId} queue status: ${message.status}`);
          break;

        case 'test_start':
          console.log(`🚀 ${userId} test execution started`);
          break;

        case 'test_output':
          console.log(`📄 ${userId} output: ${message.output}`);
          break;

        case 'test_complete':
          console.log(`✅ ${userId} test completed with status: ${message.status}`);
          ws.close();
          break;

        case 'error':
        case 'test_error':
          console.log(`❌ ${userId} error: ${message.message}`);
          break;

        default:
          console.log(`📨 ${userId} received: ${message.type}`);
      }
    });

    ws.on('error', (error) => {
      console.error(`❌ ${userId} WebSocket error:`, error.message);
    });

    ws.on('close', () => {
      console.log(`👋 ${userId} disconnected`);
    });

    connections.push({ ws, userId });
  }

  // Monitor queue stats
  const statsInterval = setInterval(async () => {
    try {
      const response = await fetch('http://localhost:3021/api/queue/stats');
      const stats = await response.json();
      if (stats.data.total > 0) {
        console.log(`📊 Queue Stats: ${stats.data.waiting} waiting, ${stats.data.active} active, ${stats.data.completed} completed`);
      }
    } catch (error) {
      // Ignore fetch errors
    }
  }, 2000);

  // Clean up after 30 seconds
  setTimeout(() => {
    clearInterval(statsInterval);
    connections.forEach(({ ws, userId }) => {
      if (ws.readyState === WebSocket.OPEN) {
        console.log(`🧹 Closing ${userId} connection`);
        ws.close();
      }
    });
    console.log('\n🎉 Queue test completed!');
    process.exit(0);
  }, 30000);
}

// Run the test
testQueue().catch(console.error);
